* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    overflow: hidden;
    height: 100vh;
}

#gameContainer {
    position: relative;
    width: 100vw;
    height: 100vh;
}

/* 游戏UI界面 */
#gameUI {
    position: absolute;
    top: 20px;
    left: 20px;
    z-index: 100;
    color: white;
    font-size: 18px;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

#gameUI div {
    margin-bottom: 10px;
    background: rgba(0, 0, 0, 0.5);
    padding: 8px 15px;
    border-radius: 5px;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

/* 开始界面 */
#startScreen {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: white;
    z-index: 200;
    background: rgba(0, 0, 0, 0.8);
    padding: 40px;
    border-radius: 15px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.8);
}

#startScreen h1 {
    font-size: 48px;
    margin-bottom: 20px;
    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8);
    color: #FFD700;
}

#startScreen p {
    font-size: 18px;
    margin-bottom: 15px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

#startButton, #restartButton {
    background: linear-gradient(45deg, #FF6B6B, #FF8E53);
    color: white;
    border: none;
    padding: 15px 30px;
    font-size: 20px;
    font-weight: bold;
    border-radius: 10px;
    cursor: pointer;
    margin-top: 20px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

#startButton:hover, #restartButton:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
    background: linear-gradient(45deg, #FF8E53, #FF6B6B);
}

/* 游戏结束界面 */
#gameOverScreen {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: white;
    z-index: 200;
    background: rgba(0, 0, 0, 0.9);
    padding: 40px;
    border-radius: 15px;
    border: 3px solid rgba(255, 0, 0, 0.5);
    box-shadow: 0 0 30px rgba(255, 0, 0, 0.3);
}

#gameOverScreen h1 {
    font-size: 42px;
    margin-bottom: 20px;
    color: #FF4444;
    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8);
}

/* 暂停界面 */
#pauseScreen {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: white;
    z-index: 200;
    background: rgba(0, 0, 0, 0.8);
    padding: 40px;
    border-radius: 15px;
    border: 3px solid rgba(255, 255, 0, 0.5);
    box-shadow: 0 0 30px rgba(255, 255, 0, 0.3);
}

#pauseScreen h1 {
    font-size: 36px;
    margin-bottom: 20px;
    color: #FFFF00;
    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8);
}

/* 响应式设计 */
@media (max-width: 768px) {
    #gameUI {
        font-size: 14px;
    }
    
    #startScreen h1 {
        font-size: 36px;
    }
    
    #startScreen p {
        font-size: 16px;
    }
    
    #startButton, #restartButton {
        font-size: 18px;
        padding: 12px 25px;
    }
}

/* 加载动画 */
.loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 24px;
    z-index: 300;
}

.loading::after {
    content: '';
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { content: '加载中.'; }
    33% { content: '加载中..'; }
    66% { content: '加载中...'; }
    100% { content: '加载中.'; }
}
