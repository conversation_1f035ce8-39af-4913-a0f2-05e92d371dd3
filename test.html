<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>游戏测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #333;
            color: white;
        }
        #status {
            background: #444;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .error {
            color: #ff6b6b;
        }
        .success {
            color: #51cf66;
        }
    </style>
</head>
<body>
    <h1>空战游戏测试页面</h1>
    
    <div id="status">
        <h3>加载状态:</h3>
        <div id="loadStatus">检查中...</div>
    </div>
    
    <button onclick="testThreeJS()">测试 Three.js</button>
    <button onclick="testAudio()">测试 AudioManager</button>
    <button onclick="testGame()">测试游戏初始化</button>
    <button onclick="location.href='index.html'">返回游戏</button>
    
    <div id="gameContainer" style="margin-top: 20px;"></div>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r180/three.min.js"></script>
    <script src="audio.js"></script>
    <script>
        const statusDiv = document.getElementById('loadStatus');
        
        function updateStatus(message, isError = false) {
            const span = document.createElement('div');
            span.textContent = message;
            span.className = isError ? 'error' : 'success';
            statusDiv.appendChild(span);
            console.log(message);
        }
        
        function testThreeJS() {
            if (typeof THREE !== 'undefined') {
                updateStatus('✓ Three.js 加载成功 (版本: ' + THREE.REVISION + ')');
                
                // 测试基本Three.js功能
                try {
                    const scene = new THREE.Scene();
                    const camera = new THREE.PerspectiveCamera(75, 1, 0.1, 1000);
                    const renderer = new THREE.WebGLRenderer();
                    updateStatus('✓ Three.js 基本功能正常');
                } catch (error) {
                    updateStatus('✗ Three.js 功能测试失败: ' + error.message, true);
                }
            } else {
                updateStatus('✗ Three.js 未加载', true);
            }
        }
        
        function testAudio() {
            if (typeof AudioManager !== 'undefined') {
                updateStatus('✓ AudioManager 类已定义');
                
                try {
                    const audioManager = new AudioManager();
                    updateStatus('✓ AudioManager 实例创建成功');
                } catch (error) {
                    updateStatus('✗ AudioManager 实例创建失败: ' + error.message, true);
                }
            } else {
                updateStatus('✗ AudioManager 类未定义', true);
            }
        }
        
        function testGame() {
            // 先加载游戏脚本
            const script = document.createElement('script');
            script.src = 'game.js';
            script.onload = function() {
                updateStatus('✓ game.js 加载成功');
                
                if (typeof AirCombatGame !== 'undefined') {
                    updateStatus('✓ AirCombatGame 类已定义');
                    
                    try {
                        // 创建必要的DOM元素
                        const gameContainer = document.getElementById('gameContainer');
                        gameContainer.innerHTML = `
                            <div id="gameUI" style="display: none;">
                                <div id="score">得分: 0</div>
                                <div id="health">生命值: 100</div>
                            </div>
                            <div id="startScreen">
                                <button id="startButton">开始游戏</button>
                            </div>
                            <div id="gameOverScreen" style="display: none;">
                                <button id="restartButton">重新开始</button>
                            </div>
                            <div id="pauseScreen" style="display: none;"></div>
                        `;
                        
                        const game = new AirCombatGame();
                        updateStatus('✓ 游戏实例创建成功');
                        
                        // 测试开始按钮
                        const startButton = document.getElementById('startButton');
                        if (startButton) {
                            startButton.onclick = function() {
                                updateStatus('✓ 开始按钮点击测试成功');
                                game.startGame();
                            };
                            updateStatus('✓ 开始按钮事件绑定成功');
                        }
                        
                    } catch (error) {
                        updateStatus('✗ 游戏实例创建失败: ' + error.message, true);
                        console.error('详细错误:', error);
                    }
                } else {
                    updateStatus('✗ AirCombatGame 类未定义', true);
                }
            };
            script.onerror = function() {
                updateStatus('✗ game.js 加载失败', true);
            };
            document.head.appendChild(script);
        }
        
        // 页面加载时自动检查
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('页面加载完成，开始检查...');
            
            setTimeout(() => {
                testThreeJS();
                testAudio();
            }, 100);
        });
    </script>
</body>
</html>
