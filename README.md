# 空战游戏 - Three.js

一个基于Three.js开发的3D空战游戏，玩家控制飞机在天空中与敌机战斗。

## 游戏特性

- **3D图形渲染**: 使用Three.js引擎实现流畅的3D图形
- **飞机控制**: 支持键盘和鼠标控制飞机移动和视角
- **武器系统**: 发射炮弹攻击敌机
- **敌机AI**: 智能敌机会主动追击玩家
- **粒子效果**: 爆炸和击中效果的粒子系统
- **音效系统**: 程序生成的射击、爆炸和击中音效
- **得分系统**: 击毁敌机获得分数
- **生命值系统**: 玩家有生命值，被敌机撞击会受到伤害

## 游戏控制

### 键盘控制
- **W**: 向前飞行
- **S**: 向后飞行
- **A**: 向左飞行（同时倾斜机身）
- **D**: 向右飞行（同时倾斜机身）
- **空格键**: 发射炮弹
- **ESC**: 暂停/继续游戏

### 鼠标控制
- **鼠标移动**: 控制飞机的俯仰和偏航角度
- **相机跟随**: 相机会自动跟随玩家飞机

## 游戏机制

### 战斗系统
- 玩家可以发射炮弹攻击敌机
- 击中敌机获得100分
- 敌机会主动追击玩家
- 被敌机撞击会损失20点生命值
- 生命值归零游戏结束

### 敌机系统
- 敌机会随机从远处生成
- 敌机具有简单的AI，会朝向玩家移动
- 最多同时存在10架敌机
- 敌机被击中后会产生爆炸效果和粒子

### 视觉效果
- 动态天空和云朵
- 爆炸粒子效果
- 阴影和光照系统
- 流畅的相机跟随

## 技术实现

### 使用的技术
- **Three.js r180**: 3D图形渲染引擎
- **Web Audio API**: 程序生成音效
- **HTML5 Canvas**: 渲染目标
- **JavaScript ES6+**: 游戏逻辑实现

### 文件结构
```
game-test/
├── index.html          # 主HTML文件
├── style.css           # 样式文件
├── game.js             # 游戏主逻辑
├── audio.js            # 音效管理器
└── README.md           # 说明文档
```

### 核心类
- **AirCombatGame**: 主游戏类，管理所有游戏逻辑
- **AudioManager**: 音效管理类，处理所有音效生成和播放

## 运行游戏

1. 确保所有文件都在同一目录下
2. 使用现代浏览器打开 `index.html` 文件
3. 点击"开始游戏"按钮开始游戏

### 系统要求
- 支持WebGL的现代浏览器
- 支持Web Audio API（用于音效）
- 建议使用Chrome、Firefox、Safari或Edge最新版本

## 游戏截图和演示

游戏包含以下视觉元素：
- 蓝色玩家飞机（可控制）
- 红色敌机（AI控制）
- 黄色炮弹轨迹
- 橙色爆炸效果
- 彩色粒子系统
- 动态云朵和天空

## 开发说明

### 扩展功能建议
- 添加更多武器类型
- 实现不同类型的敌机
- 添加关卡系统
- 实现多人游戏模式
- 添加更复杂的地形
- 实现飞机升级系统

### 性能优化
- 游戏已实现基本的对象池管理
- 自动清理超出范围的对象
- 使用LOD（细节层次）可以进一步优化性能

## 许可证

本项目仅供学习和演示使用。

## 致谢

- Three.js团队提供的优秀3D引擎
- Web Audio API使音效成为可能
- 现代浏览器的WebGL支持
