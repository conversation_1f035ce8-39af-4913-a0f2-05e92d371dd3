// 调试版本的空战游戏
console.log('开始加载调试版游戏');

class SimpleAirCombatGame {
    constructor() {
        console.log('游戏构造函数被调用');
        
        // 游戏状态
        this.gameState = 'start';
        this.score = 0;
        this.health = 100;
        
        // Three.js 核心对象
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        
        // 游戏对象
        this.player = null;
        this.bullets = [];
        this.enemies = [];
        
        // 控制相关
        this.keys = {};
        
        // 初始化
        this.init();
        this.setupEventListeners();
        
        console.log('游戏构造完成');
    }
    
    init() {
        console.log('初始化游戏');
        
        // 创建场景
        this.scene = new THREE.Scene();
        this.scene.fog = new THREE.Fog(0x87CEEB, 50, 200);
        
        // 创建相机
        this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        this.camera.position.set(0, 5, 10);
        
        // 创建渲染器
        this.renderer = new THREE.WebGLRenderer({ antialias: true });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setClearColor(0x87CEEB);
        
        const gameContainer = document.getElementById('gameContainer');
        if (gameContainer) {
            gameContainer.appendChild(this.renderer.domElement);
            console.log('渲染器已添加到容器');
        } else {
            console.error('找不到gameContainer元素');
            return;
        }
        
        // 添加光照
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);
        
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
        directionalLight.position.set(50, 50, 50);
        this.scene.add(directionalLight);
        
        // 创建玩家飞机
        this.createPlayer();
        
        console.log('游戏初始化完成');
    }
    
    createPlayer() {
        console.log('创建玩家飞机');
        
        this.player = new THREE.Group();
        
        // 机身
        const fuselageGeometry = new THREE.CylinderGeometry(0.3, 0.5, 4, 8);
        const fuselageMaterial = new THREE.MeshLambertMaterial({ color: 0x4169E1 });
        const fuselage = new THREE.Mesh(fuselageGeometry, fuselageMaterial);
        fuselage.rotation.z = Math.PI / 2;
        this.player.add(fuselage);
        
        // 机翼
        const wingGeometry = new THREE.BoxGeometry(6, 0.2, 1.5);
        const wingMaterial = new THREE.MeshLambertMaterial({ color: 0x4169E1 });
        const wings = new THREE.Mesh(wingGeometry, wingMaterial);
        this.player.add(wings);
        
        this.player.position.set(0, 0, 0);
        this.scene.add(this.player);
        
        console.log('玩家飞机创建完成');
    }
    
    setupEventListeners() {
        console.log('设置事件监听器');
        
        // 键盘事件
        document.addEventListener('keydown', (event) => {
            this.keys[event.code] = true;
            if (event.code === 'Space') {
                event.preventDefault();
                if (this.gameState === 'playing') {
                    this.shootBullet();
                }
            }
        });
        
        document.addEventListener('keyup', (event) => {
            this.keys[event.code] = false;
        });
        
        // 窗口大小调整
        window.addEventListener('resize', () => {
            if (this.camera && this.renderer) {
                this.camera.aspect = window.innerWidth / window.innerHeight;
                this.camera.updateProjectionMatrix();
                this.renderer.setSize(window.innerWidth, window.innerHeight);
            }
        });
        
        // UI按钮事件
        const startButton = document.getElementById('startButton');
        if (startButton) {
            startButton.addEventListener('click', () => {
                console.log('开始游戏按钮被点击');
                this.startGame();
            });
            console.log('开始按钮事件监听器已设置');
        } else {
            console.error('找不到开始按钮元素');
        }
        
        console.log('事件监听器设置完成');
    }
    
    startGame() {
        console.log('startGame 方法被调用');
        
        this.gameState = 'playing';
        
        const startScreen = document.getElementById('startScreen');
        const gameUI = document.getElementById('gameUI');
        
        if (startScreen) {
            startScreen.style.display = 'none';
            console.log('开始界面已隐藏');
        }
        
        if (gameUI) {
            gameUI.style.display = 'block';
            console.log('游戏UI已显示');
        }
        
        console.log('开始动画循环');
        this.animate();
    }
    
    shootBullet() {
        const bulletGeometry = new THREE.SphereGeometry(0.1, 8, 8);
        const bulletMaterial = new THREE.MeshBasicMaterial({ color: 0xFFFF00 });
        const bullet = new THREE.Mesh(bulletGeometry, bulletMaterial);
        
        bullet.position.copy(this.player.position);
        bullet.userData.velocity = new THREE.Vector3(0, 0, -2);
        
        this.bullets.push(bullet);
        this.scene.add(bullet);
    }
    
    updatePlayer() {
        if (this.gameState !== 'playing') return;
        
        const moveSpeed = 0.5;
        
        if (this.keys['KeyW']) this.player.position.z -= moveSpeed;
        if (this.keys['KeyS']) this.player.position.z += moveSpeed;
        if (this.keys['KeyA']) this.player.position.x -= moveSpeed;
        if (this.keys['KeyD']) this.player.position.x += moveSpeed;
        
        // 限制移动范围
        this.player.position.x = Math.max(-20, Math.min(20, this.player.position.x));
        this.player.position.z = Math.max(-20, Math.min(20, this.player.position.z));
        
        // 更新相机
        this.camera.position.set(
            this.player.position.x,
            this.player.position.y + 5,
            this.player.position.z + 10
        );
        this.camera.lookAt(this.player.position);
    }
    
    updateBullets() {
        for (let i = this.bullets.length - 1; i >= 0; i--) {
            const bullet = this.bullets[i];
            bullet.position.add(bullet.userData.velocity);
            
            if (bullet.position.length() > 50) {
                this.scene.remove(bullet);
                this.bullets.splice(i, 1);
            }
        }
    }
    
    spawnEnemy() {
        if (this.enemies.length < 3 && Math.random() < 0.01) {
            const enemy = new THREE.Group();
            
            const fuselageGeometry = new THREE.CylinderGeometry(0.2, 0.3, 3, 6);
            const fuselageMaterial = new THREE.MeshLambertMaterial({ color: 0xFF4444 });
            const fuselage = new THREE.Mesh(fuselageGeometry, fuselageMaterial);
            fuselage.rotation.z = Math.PI / 2;
            enemy.add(fuselage);
            
            const angle = Math.random() * Math.PI * 2;
            enemy.position.set(
                Math.cos(angle) * 30,
                Math.random() * 10 - 5,
                Math.sin(angle) * 30
            );
            
            this.enemies.push(enemy);
            this.scene.add(enemy);
        }
    }
    
    updateEnemies() {
        for (let i = this.enemies.length - 1; i >= 0; i--) {
            const enemy = this.enemies[i];
            
            const direction = new THREE.Vector3();
            direction.subVectors(this.player.position, enemy.position);
            direction.normalize();
            enemy.position.add(direction.multiplyScalar(0.2));
            
            const distance = enemy.position.distanceTo(this.player.position);
            if (distance < 2) {
                this.health -= 20;
                this.scene.remove(enemy);
                this.enemies.splice(i, 1);
                this.updateUI();
                
                if (this.health <= 0) {
                    alert('游戏结束！得分：' + this.score);
                    location.reload();
                }
            }
        }
    }
    
    checkCollisions() {
        for (let i = this.bullets.length - 1; i >= 0; i--) {
            const bullet = this.bullets[i];
            
            for (let j = this.enemies.length - 1; j >= 0; j--) {
                const enemy = this.enemies[j];
                
                const distance = bullet.position.distanceTo(enemy.position);
                if (distance < 1.5) {
                    this.score += 100;
                    this.scene.remove(bullet);
                    this.scene.remove(enemy);
                    this.bullets.splice(i, 1);
                    this.enemies.splice(j, 1);
                    this.updateUI();
                    break;
                }
            }
        }
    }
    
    updateUI() {
        const scoreElement = document.getElementById('score');
        const healthElement = document.getElementById('health');
        
        if (scoreElement) scoreElement.textContent = `得分: ${this.score}`;
        if (healthElement) healthElement.textContent = `生命值: ${this.health}`;
    }
    
    animate() {
        if (this.gameState === 'playing') {
            requestAnimationFrame(() => this.animate());
            
            this.updatePlayer();
            this.updateBullets();
            this.updateEnemies();
            this.spawnEnemy();
            this.checkCollisions();
            
            this.renderer.render(this.scene, this.camera);
        }
    }
}

// 游戏初始化
let debugGame;

document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM加载完成，开始初始化调试游戏');
    
    if (typeof THREE === 'undefined') {
        console.error('Three.js 未能正确加载');
        document.body.innerHTML = '<div style="color: red; text-align: center; margin-top: 50px;">Three.js 加载失败，请检查网络连接</div>';
        return;
    }
    
    console.log('Three.js 加载成功');
    
    try {
        debugGame = new SimpleAirCombatGame();
        console.log('调试游戏初始化成功');
    } catch (error) {
        console.error('调试游戏初始化失败:', error);
        document.body.innerHTML = '<div style="color: red; text-align: center; margin-top: 50px;">游戏初始化失败: ' + error.message + '</div>';
    }
});

console.log('调试游戏脚本加载完成');
