<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>空战游戏 - 本地版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            overflow: hidden;
            height: 100vh;
        }

        #gameContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        #gameUI {
            position: absolute;
            top: 20px;
            left: 20px;
            z-index: 100;
            color: white;
            font-size: 18px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            display: none;
        }

        #gameUI div {
            margin-bottom: 10px;
            background: rgba(0, 0, 0, 0.5);
            padding: 8px 15px;
            border-radius: 5px;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        #startScreen {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: white;
            z-index: 200;
            background: rgba(0, 0, 0, 0.8);
            padding: 40px;
            border-radius: 15px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 0 30px rgba(0, 0, 0, 0.8);
        }

        #startScreen h1 {
            font-size: 48px;
            margin-bottom: 20px;
            text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8);
            color: #FFD700;
        }

        #startScreen p {
            font-size: 18px;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
        }

        #startButton, #restartButton {
            background: linear-gradient(45deg, #FF6B6B, #FF8E53);
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 20px;
            font-weight: bold;
            border-radius: 10px;
            cursor: pointer;
            margin-top: 20px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        #startButton:hover, #restartButton:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
            background: linear-gradient(45deg, #FF8E53, #FF6B6B);
        }

        #gameOverScreen {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: white;
            z-index: 200;
            background: rgba(0, 0, 0, 0.9);
            padding: 40px;
            border-radius: 15px;
            border: 3px solid rgba(255, 0, 0, 0.5);
            box-shadow: 0 0 30px rgba(255, 0, 0, 0.3);
            display: none;
        }

        #gameOverScreen h1 {
            font-size: 42px;
            margin-bottom: 20px;
            color: #FF4444;
            text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8);
        }

        .status {
            position: absolute;
            bottom: 20px;
            left: 20px;
            color: white;
            font-size: 14px;
            background: rgba(0, 0, 0, 0.5);
            padding: 10px;
            border-radius: 5px;
            z-index: 150;
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <div id="gameUI">
            <div id="score">得分: 0</div>
            <div id="health">生命值: 100</div>
            <div>WASD移动 | 空格射击 | 鼠标控制视角</div>
        </div>
        
        <div id="startScreen">
            <h1>空战游戏</h1>
            <p>🛩️ 使用 WASD 控制飞机移动</p>
            <p>🎯 使用 空格键 发射炮弹</p>
            <p>🖱️ 使用 鼠标 控制视角</p>
            <p>💥 击毁红色敌机获得分数</p>
            <button id="startButton">开始游戏</button>
        </div>
        
        <div id="gameOverScreen">
            <h1>游戏结束</h1>
            <p id="finalScore">最终得分: 0</p>
            <button id="restartButton">重新开始</button>
        </div>
        
        <div class="status" id="loadStatus">
            正在加载Three.js...
        </div>
    </div>
    
    <!-- 使用本地Three.js文件 -->
    <script src="three.min.js"></script>
    <script>
        // 更新加载状态
        document.getElementById('loadStatus').textContent = 'Three.js 加载成功！';
        
        class AirCombatGame {
            constructor() {
                this.gameState = 'start';
                this.score = 0;
                this.health = 100;
                this.maxHealth = 100;
                
                this.scene = null;
                this.camera = null;
                this.renderer = null;
                this.clock = new THREE.Clock();
                
                this.player = null;
                this.bullets = [];
                this.enemies = [];
                this.explosions = [];
                
                this.keys = {};
                this.mouse = { x: 0, y: 0 };
                
                this.bulletSpeed = 2;
                this.enemySpeed = 0.4;
                this.spawnRate = 0.02;
                this.maxEnemies = 10;
                
                this.init();
                this.setupEventListeners();
                
                // 隐藏加载状态
                setTimeout(() => {
                    document.getElementById('loadStatus').style.display = 'none';
                }, 2000);
            }
            
            init() {
                console.log('初始化游戏场景');
                
                this.scene = new THREE.Scene();
                this.scene.fog = new THREE.Fog(0x87CEEB, 50, 200);
                
                this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
                this.camera.position.set(0, 5, 10);
                
                this.renderer = new THREE.WebGLRenderer({ antialias: true });
                this.renderer.setSize(window.innerWidth, window.innerHeight);
                this.renderer.setClearColor(0x87CEEB);
                this.renderer.shadowMap.enabled = true;
                this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
                
                document.getElementById('gameContainer').appendChild(this.renderer.domElement);
                
                this.setupLighting();
                this.createEnvironment();
                this.createPlayer();
                
                console.log('游戏场景初始化完成');
            }
            
            setupLighting() {
                const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
                this.scene.add(ambientLight);
                
                const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
                directionalLight.position.set(50, 50, 50);
                directionalLight.castShadow = true;
                directionalLight.shadow.mapSize.width = 2048;
                directionalLight.shadow.mapSize.height = 2048;
                this.scene.add(directionalLight);
            }
            
            createEnvironment() {
                // 地面
                const groundGeometry = new THREE.PlaneGeometry(200, 200);
                const groundMaterial = new THREE.MeshLambertMaterial({ 
                    color: 0x90EE90,
                    transparent: true,
                    opacity: 0.8
                });
                const ground = new THREE.Mesh(groundGeometry, groundMaterial);
                ground.rotation.x = -Math.PI / 2;
                ground.position.y = -20;
                ground.receiveShadow = true;
                this.scene.add(ground);
                
                // 云朵
                for (let i = 0; i < 20; i++) {
                    const cloudGeometry = new THREE.SphereGeometry(5, 8, 6);
                    const cloudMaterial = new THREE.MeshLambertMaterial({ 
                        color: 0xffffff,
                        transparent: true,
                        opacity: 0.7
                    });
                    const cloud = new THREE.Mesh(cloudGeometry, cloudMaterial);
                    cloud.position.set(
                        (Math.random() - 0.5) * 200,
                        Math.random() * 30 + 10,
                        (Math.random() - 0.5) * 200
                    );
                    cloud.scale.set(
                        Math.random() * 2 + 1,
                        Math.random() * 1 + 0.5,
                        Math.random() * 2 + 1
                    );
                    this.scene.add(cloud);
                }
                
                // 天空盒
                const skyGeometry = new THREE.SphereGeometry(300, 32, 32);
                const skyMaterial = new THREE.MeshBasicMaterial({
                    color: 0x87CEEB,
                    side: THREE.BackSide
                });
                const sky = new THREE.Mesh(skyGeometry, skyMaterial);
                this.scene.add(sky);
            }
            
            createPlayer() {
                this.player = new THREE.Group();
                
                // 机身
                const fuselageGeometry = new THREE.CylinderGeometry(0.3, 0.5, 4, 8);
                const fuselageMaterial = new THREE.MeshLambertMaterial({ color: 0x4169E1 });
                const fuselage = new THREE.Mesh(fuselageGeometry, fuselageMaterial);
                fuselage.rotation.z = Math.PI / 2;
                fuselage.castShadow = true;
                this.player.add(fuselage);
                
                // 机翼
                const wingGeometry = new THREE.BoxGeometry(6, 0.2, 1.5);
                const wingMaterial = new THREE.MeshLambertMaterial({ color: 0x4169E1 });
                const wings = new THREE.Mesh(wingGeometry, wingMaterial);
                wings.castShadow = true;
                this.player.add(wings);
                
                // 尾翼
                const tailGeometry = new THREE.BoxGeometry(0.5, 2, 0.2);
                const tailMaterial = new THREE.MeshLambertMaterial({ color: 0x4169E1 });
                const tail = new THREE.Mesh(tailGeometry, tailMaterial);
                tail.position.set(-1.5, 0, 0);
                tail.castShadow = true;
                this.player.add(tail);
                
                this.player.position.set(0, 0, 0);
                this.scene.add(this.player);
            }
            
            setupEventListeners() {
                document.addEventListener('keydown', (event) => {
                    this.keys[event.code] = true;
                    if (event.code === 'Space') {
                        event.preventDefault();
                        if (this.gameState === 'playing') {
                            this.shootBullet();
                        }
                    }
                    if (event.code === 'Escape') {
                        event.preventDefault();
                        this.togglePause();
                    }
                });
                
                document.addEventListener('keyup', (event) => {
                    this.keys[event.code] = false;
                });
                
                document.addEventListener('mousemove', (event) => {
                    this.mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
                    this.mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
                });
                
                window.addEventListener('resize', () => {
                    this.camera.aspect = window.innerWidth / window.innerHeight;
                    this.camera.updateProjectionMatrix();
                    this.renderer.setSize(window.innerWidth, window.innerHeight);
                });
                
                document.getElementById('startButton').addEventListener('click', () => {
                    this.startGame();
                });
                
                document.getElementById('restartButton').addEventListener('click', () => {
                    this.restartGame();
                });
            }
            
            startGame() {
                console.log('游戏开始');
                this.gameState = 'playing';
                document.getElementById('startScreen').style.display = 'none';
                document.getElementById('gameUI').style.display = 'block';
                this.animate();
            }
            
            restartGame() {
                this.score = 0;
                this.health = this.maxHealth;
                this.bullets = [];
                this.enemies = [];
                this.explosions = [];
                
                // 清理场景
                this.bullets.forEach(bullet => this.scene.remove(bullet));
                this.enemies.forEach(enemy => this.scene.remove(enemy));
                this.explosions.forEach(explosion => this.scene.remove(explosion));
                
                this.player.position.set(0, 0, 0);
                this.player.rotation.set(0, 0, 0);
                
                this.updateUI();
                document.getElementById('gameOverScreen').style.display = 'none';
                this.startGame();
            }
            
            togglePause() {
                if (this.gameState === 'playing') {
                    this.gameState = 'paused';
                    alert('游戏暂停 - 按ESC继续');
                } else if (this.gameState === 'paused') {
                    this.gameState = 'playing';
                    this.animate();
                }
            }
            
            shootBullet() {
                const bulletGeometry = new THREE.SphereGeometry(0.1, 8, 8);
                const bulletMaterial = new THREE.MeshBasicMaterial({ color: 0xFFFF00 });
                const bullet = new THREE.Mesh(bulletGeometry, bulletMaterial);
                
                bullet.position.copy(this.player.position);
                bullet.position.y += 0.5;
                
                const direction = new THREE.Vector3(0, 0, -1);
                direction.applyQuaternion(this.player.quaternion);
                bullet.userData.direction = direction;
                bullet.userData.speed = this.bulletSpeed;
                
                this.bullets.push(bullet);
                this.scene.add(bullet);
            }
            
            updatePlayerMovement() {
                if (this.gameState !== 'playing') return;
                
                const moveSpeed = 0.5;
                const rotationSpeed = 0.05;
                
                if (this.keys['KeyW']) this.player.position.z -= moveSpeed;
                if (this.keys['KeyS']) this.player.position.z += moveSpeed;
                if (this.keys['KeyA']) {
                    this.player.position.x -= moveSpeed;
                    this.player.rotation.z = Math.min(this.player.rotation.z + rotationSpeed, 0.3);
                }
                if (this.keys['KeyD']) {
                    this.player.position.x += moveSpeed;
                    this.player.rotation.z = Math.max(this.player.rotation.z - rotationSpeed, -0.3);
                }
                
                if (!this.keys['KeyA'] && !this.keys['KeyD']) {
                    this.player.rotation.z *= 0.9;
                }
                
                this.player.rotation.y = this.mouse.x * 0.3;
                this.player.rotation.x = this.mouse.y * 0.2;
                
                this.player.position.x = Math.max(-50, Math.min(50, this.player.position.x));
                this.player.position.z = Math.max(-50, Math.min(50, this.player.position.z));
                this.player.position.y = Math.max(-10, Math.min(20, this.player.position.y));
                
                this.updateCamera();
            }
            
            updateCamera() {
                const targetPosition = new THREE.Vector3(
                    this.player.position.x,
                    this.player.position.y + 5,
                    this.player.position.z + 10
                );
                this.camera.position.lerp(targetPosition, 0.1);
                this.camera.lookAt(this.player.position);
            }
            
            updateBullets() {
                for (let i = this.bullets.length - 1; i >= 0; i--) {
                    const bullet = this.bullets[i];
                    bullet.position.add(bullet.userData.direction.clone().multiplyScalar(bullet.userData.speed));
                    
                    if (bullet.position.length() > 100) {
                        this.scene.remove(bullet);
                        this.bullets.splice(i, 1);
                    }
                }
            }
            
            spawnEnemy() {
                if (this.enemies.length >= this.maxEnemies || Math.random() > this.spawnRate) return;
                
                const enemy = new THREE.Group();
                
                const fuselageGeometry = new THREE.CylinderGeometry(0.2, 0.3, 3, 6);
                const fuselageMaterial = new THREE.MeshLambertMaterial({ color: 0xFF4444 });
                const fuselage = new THREE.Mesh(fuselageGeometry, fuselageMaterial);
                fuselage.rotation.z = Math.PI / 2;
                fuselage.castShadow = true;
                enemy.add(fuselage);
                
                const wingGeometry = new THREE.BoxGeometry(4, 0.1, 1);
                const wingMaterial = new THREE.MeshLambertMaterial({ color: 0xFF4444 });
                const wings = new THREE.Mesh(wingGeometry, wingMaterial);
                wings.castShadow = true;
                enemy.add(wings);
                
                const spawnDistance = 80;
                const angle = Math.random() * Math.PI * 2;
                enemy.position.set(
                    Math.cos(angle) * spawnDistance,
                    Math.random() * 20 - 10,
                    Math.sin(angle) * spawnDistance
                );
                
                enemy.userData.health = 1;
                enemy.userData.speed = this.enemySpeed;
                
                this.enemies.push(enemy);
                this.scene.add(enemy);
            }
            
            updateEnemies() {
                for (let i = this.enemies.length - 1; i >= 0; i--) {
                    const enemy = this.enemies[i];
                    
                    const direction = new THREE.Vector3();
                    direction.subVectors(this.player.position, enemy.position);
                    direction.normalize();
                    enemy.position.add(direction.multiplyScalar(enemy.userData.speed));
                    enemy.lookAt(this.player.position);
                    
                    const distance = enemy.position.distanceTo(this.player.position);
                    if (distance < 2) {
                        this.takeDamage(20);
                        this.createExplosion(enemy.position);
                        this.scene.remove(enemy);
                        this.enemies.splice(i, 1);
                    } else if (distance > 150) {
                        this.scene.remove(enemy);
                        this.enemies.splice(i, 1);
                    }
                }
            }
            
            checkCollisions() {
                for (let i = this.bullets.length - 1; i >= 0; i--) {
                    const bullet = this.bullets[i];
                    
                    for (let j = this.enemies.length - 1; j >= 0; j--) {
                        const enemy = this.enemies[j];
                        
                        const distance = bullet.position.distanceTo(enemy.position);
                        if (distance < 1.5) {
                            this.score += 100;
                            this.createExplosion(enemy.position);
                            this.scene.remove(bullet);
                            this.scene.remove(enemy);
                            this.bullets.splice(i, 1);
                            this.enemies.splice(j, 1);
                            break;
                        }
                    }
                }
            }
            
            createExplosion(position) {
                const explosionGeometry = new THREE.SphereGeometry(2, 8, 8);
                const explosionMaterial = new THREE.MeshBasicMaterial({ 
                    color: 0xFF6600,
                    transparent: true,
                    opacity: 0.8
                });
                const explosion = new THREE.Mesh(explosionGeometry, explosionMaterial);
                explosion.position.copy(position);
                explosion.userData.life = 1.0;
                explosion.userData.maxScale = 3;
                
                this.explosions.push(explosion);
                this.scene.add(explosion);
            }
            
            updateExplosions() {
                for (let i = this.explosions.length - 1; i >= 0; i--) {
                    const explosion = this.explosions[i];
                    explosion.userData.life -= 0.05;
                    
                    if (explosion.userData.life <= 0) {
                        this.scene.remove(explosion);
                        this.explosions.splice(i, 1);
                    } else {
                        const scale = (1 - explosion.userData.life) * explosion.userData.maxScale;
                        explosion.scale.set(scale, scale, scale);
                        explosion.material.opacity = explosion.userData.life * 0.8;
                    }
                }
            }
            
            takeDamage(amount) {
                this.health -= amount;
                if (this.health <= 0) {
                    this.health = 0;
                    this.gameOver();
                }
            }
            
            gameOver() {
                this.gameState = 'gameOver';
                document.getElementById('finalScore').textContent = `最终得分: ${this.score}`;
                document.getElementById('gameOverScreen').style.display = 'block';
                document.getElementById('gameUI').style.display = 'none';
            }
            
            updateUI() {
                document.getElementById('score').textContent = `得分: ${this.score}`;
                document.getElementById('health').textContent = `生命值: ${this.health}`;
            }
            
            animate() {
                if (this.gameState === 'playing') {
                    requestAnimationFrame(() => this.animate());
                    
                    this.updatePlayerMovement();
                    this.updateBullets();
                    this.updateEnemies();
                    this.updateExplosions();
                    this.checkCollisions();
                    this.spawnEnemy();
                    this.updateUI();
                    
                    this.renderer.render(this.scene, this.camera);
                }
            }
        }
        
        let game;
        
        document.addEventListener('DOMContentLoaded', () => {
            if (typeof THREE === 'undefined') {
                document.getElementById('loadStatus').textContent = 'Three.js 加载失败！';
                document.getElementById('loadStatus').style.color = '#ff4444';
                alert('Three.js 加载失败，请检查文件是否存在');
                return;
            }
            
            try {
                game = new AirCombatGame();
                console.log('空战游戏初始化成功');
            } catch (error) {
                console.error('游戏初始化失败:', error);
                document.getElementById('loadStatus').textContent = '游戏初始化失败: ' + error.message;
                document.getElementById('loadStatus').style.color = '#ff4444';
            }
        });
    </script>
</body>
</html>
