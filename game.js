// 空战游戏 - Three.js 实现
class AirCombatGame {
    constructor() {
        // 游戏状态
        this.gameState = 'start'; // start, playing, paused, gameOver
        this.score = 0;
        this.health = 100;
        this.maxHealth = 100;
        
        // Three.js 核心对象
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.clock = new THREE.Clock();
        
        // 游戏对象
        this.player = null;
        this.bullets = [];
        this.enemies = [];
        this.explosions = [];
        this.particles = [];
        
        // 控制相关
        this.keys = {};
        this.mouse = { x: 0, y: 0 };
        this.mousePressed = false;
        
        // 游戏设置
        this.bulletSpeed = 2;
        this.enemySpeed = 0.5;
        this.spawnRate = 0.02; // 敌机生成概率
        this.maxEnemies = 10;

        // 音效管理器
        this.audioManager = null;

        // 初始化
        this.init();
        this.setupEventListeners();
    }
    
    init() {
        // 创建场景
        this.scene = new THREE.Scene();
        this.scene.fog = new THREE.Fog(0x87CEEB, 50, 200);
        
        // 创建相机
        this.camera = new THREE.PerspectiveCamera(
            75, 
            window.innerWidth / window.innerHeight, 
            0.1, 
            1000
        );
        this.camera.position.set(0, 5, 10);
        
        // 创建渲染器
        this.renderer = new THREE.WebGLRenderer({ antialias: true });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setClearColor(0x87CEEB);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        
        document.getElementById('gameContainer').appendChild(this.renderer.domElement);
        
        // 添加光照
        this.setupLighting();
        
        // 创建环境
        this.createEnvironment();
        
        // 创建玩家飞机
        this.createPlayer();

        // 初始化音效管理器
        this.audioManager = new AudioManager();
    }
    
    setupLighting() {
        // 环境光
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);
        
        // 方向光（太阳光）
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
        directionalLight.position.set(50, 50, 50);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        directionalLight.shadow.camera.near = 0.5;
        directionalLight.shadow.camera.far = 500;
        this.scene.add(directionalLight);
    }
    
    createEnvironment() {
        // 创建地面
        const groundGeometry = new THREE.PlaneGeometry(200, 200);
        const groundMaterial = new THREE.MeshLambertMaterial({ 
            color: 0x90EE90,
            transparent: true,
            opacity: 0.8
        });
        const ground = new THREE.Mesh(groundGeometry, groundMaterial);
        ground.rotation.x = -Math.PI / 2;
        ground.position.y = -20;
        ground.receiveShadow = true;
        this.scene.add(ground);
        
        // 创建云朵
        this.createClouds();
        
        // 创建天空盒
        this.createSkybox();
    }
    
    createClouds() {
        const cloudGeometry = new THREE.SphereGeometry(5, 8, 6);
        const cloudMaterial = new THREE.MeshLambertMaterial({ 
            color: 0xffffff,
            transparent: true,
            opacity: 0.7
        });
        
        for (let i = 0; i < 20; i++) {
            const cloud = new THREE.Mesh(cloudGeometry, cloudMaterial);
            cloud.position.set(
                (Math.random() - 0.5) * 200,
                Math.random() * 30 + 10,
                (Math.random() - 0.5) * 200
            );
            cloud.scale.set(
                Math.random() * 2 + 1,
                Math.random() * 1 + 0.5,
                Math.random() * 2 + 1
            );
            this.scene.add(cloud);
        }
    }
    
    createSkybox() {
        const skyGeometry = new THREE.SphereGeometry(300, 32, 32);
        const skyMaterial = new THREE.MeshBasicMaterial({
            color: 0x87CEEB,
            side: THREE.BackSide
        });
        const sky = new THREE.Mesh(skyGeometry, skyMaterial);
        this.scene.add(sky);
    }
    
    createPlayer() {
        // 创建玩家飞机组
        this.player = new THREE.Group();
        
        // 机身
        const fuselageGeometry = new THREE.CylinderGeometry(0.3, 0.5, 4, 8);
        const fuselageMaterial = new THREE.MeshLambertMaterial({ color: 0x4169E1 });
        const fuselage = new THREE.Mesh(fuselageGeometry, fuselageMaterial);
        fuselage.rotation.z = Math.PI / 2;
        fuselage.castShadow = true;
        this.player.add(fuselage);
        
        // 机翼
        const wingGeometry = new THREE.BoxGeometry(6, 0.2, 1.5);
        const wingMaterial = new THREE.MeshLambertMaterial({ color: 0x4169E1 });
        const wings = new THREE.Mesh(wingGeometry, wingMaterial);
        wings.castShadow = true;
        this.player.add(wings);
        
        // 尾翼
        const tailGeometry = new THREE.BoxGeometry(0.5, 2, 0.2);
        const tailMaterial = new THREE.MeshLambertMaterial({ color: 0x4169E1 });
        const tail = new THREE.Mesh(tailGeometry, tailMaterial);
        tail.position.set(-1.5, 0, 0);
        tail.castShadow = true;
        this.player.add(tail);
        
        // 设置初始位置
        this.player.position.set(0, 0, 0);
        this.scene.add(this.player);
    }

    setupEventListeners() {
        // 键盘事件
        document.addEventListener('keydown', (event) => {
            this.keys[event.code] = true;

            // 特殊按键处理
            if (event.code === 'Space') {
                event.preventDefault();
                if (this.gameState === 'playing') {
                    this.shootBullet();
                }
            }

            if (event.code === 'Escape') {
                event.preventDefault();
                this.togglePause();
            }
        });

        document.addEventListener('keyup', (event) => {
            this.keys[event.code] = false;
        });

        // 鼠标事件
        document.addEventListener('mousemove', (event) => {
            this.mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
            this.mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
        });

        document.addEventListener('mousedown', () => {
            this.mousePressed = true;
        });

        document.addEventListener('mouseup', () => {
            this.mousePressed = false;
        });

        // 窗口大小调整
        window.addEventListener('resize', () => {
            this.camera.aspect = window.innerWidth / window.innerHeight;
            this.camera.updateProjectionMatrix();
            this.renderer.setSize(window.innerWidth, window.innerHeight);
        });

        // UI按钮事件
        document.getElementById('startButton').addEventListener('click', () => {
            this.startGame();
        });

        document.getElementById('restartButton').addEventListener('click', () => {
            this.restartGame();
        });
    }

    startGame() {
        this.gameState = 'playing';
        document.getElementById('startScreen').style.display = 'none';
        document.getElementById('gameUI').style.display = 'block';

        // 恢复音频上下文
        if (this.audioManager) {
            this.audioManager.resume();
        }

        this.animate();
    }

    restartGame() {
        // 重置游戏状态
        this.score = 0;
        this.health = this.maxHealth;
        this.bullets = [];
        this.enemies = [];
        this.explosions = [];

        // 清理场景中的对象
        this.clearGameObjects();

        // 重置玩家位置
        this.player.position.set(0, 0, 0);
        this.player.rotation.set(0, 0, 0);

        // 更新UI
        this.updateUI();

        // 隐藏游戏结束界面
        document.getElementById('gameOverScreen').style.display = 'none';

        // 开始游戏
        this.startGame();
    }

    togglePause() {
        if (this.gameState === 'playing') {
            this.gameState = 'paused';
            document.getElementById('pauseScreen').style.display = 'block';
        } else if (this.gameState === 'paused') {
            this.gameState = 'playing';
            document.getElementById('pauseScreen').style.display = 'none';
        }
    }

    clearGameObjects() {
        // 清理炮弹
        this.bullets.forEach(bullet => {
            this.scene.remove(bullet);
        });

        // 清理敌机
        this.enemies.forEach(enemy => {
            this.scene.remove(enemy);
        });

        // 清理爆炸效果
        this.explosions.forEach(explosion => {
            this.scene.remove(explosion);
        });

        // 清理粒子效果
        this.particles.forEach(particle => {
            this.scene.remove(particle);
        });
    }

    updatePlayerMovement() {
        if (this.gameState !== 'playing') return;

        const moveSpeed = 0.5;
        const rotationSpeed = 0.05;

        // WASD 移动控制
        if (this.keys['KeyW']) {
            this.player.position.z -= moveSpeed;
        }
        if (this.keys['KeyS']) {
            this.player.position.z += moveSpeed;
        }
        if (this.keys['KeyA']) {
            this.player.position.x -= moveSpeed;
            this.player.rotation.z = Math.min(this.player.rotation.z + rotationSpeed, 0.3);
        }
        if (this.keys['KeyD']) {
            this.player.position.x += moveSpeed;
            this.player.rotation.z = Math.max(this.player.rotation.z - rotationSpeed, -0.3);
        }

        // 如果没有左右移动，逐渐恢复水平
        if (!this.keys['KeyA'] && !this.keys['KeyD']) {
            this.player.rotation.z *= 0.9;
        }

        // 鼠标控制俯仰和偏航
        this.player.rotation.y = this.mouse.x * 0.3;
        this.player.rotation.x = this.mouse.y * 0.2;

        // 限制移动范围
        this.player.position.x = Math.max(-50, Math.min(50, this.player.position.x));
        this.player.position.z = Math.max(-50, Math.min(50, this.player.position.z));
        this.player.position.y = Math.max(-10, Math.min(20, this.player.position.y));

        // 更新相机跟随
        this.updateCamera();
    }

    updateCamera() {
        // 相机跟随玩家
        const targetPosition = new THREE.Vector3(
            this.player.position.x,
            this.player.position.y + 5,
            this.player.position.z + 10
        );

        this.camera.position.lerp(targetPosition, 0.1);
        this.camera.lookAt(this.player.position);
    }

    shootBullet() {
        // 创建炮弹
        const bulletGeometry = new THREE.SphereGeometry(0.1, 8, 8);
        const bulletMaterial = new THREE.MeshBasicMaterial({ color: 0xFFFF00 });
        const bullet = new THREE.Mesh(bulletGeometry, bulletMaterial);

        // 设置炮弹初始位置和方向
        bullet.position.copy(this.player.position);
        bullet.position.y += 0.5;

        // 根据玩家朝向设置炮弹方向
        const direction = new THREE.Vector3(0, 0, -1);
        direction.applyQuaternion(this.player.quaternion);
        bullet.userData.direction = direction;
        bullet.userData.speed = this.bulletSpeed;

        this.bullets.push(bullet);
        this.scene.add(bullet);

        // 播放射击音效
        if (this.audioManager) {
            this.audioManager.playSound('shoot');
        }
    }

    updateBullets() {
        for (let i = this.bullets.length - 1; i >= 0; i--) {
            const bullet = this.bullets[i];

            // 移动炮弹
            bullet.position.add(
                bullet.userData.direction.clone().multiplyScalar(bullet.userData.speed)
            );

            // 移除超出范围的炮弹
            if (bullet.position.length() > 100) {
                this.scene.remove(bullet);
                this.bullets.splice(i, 1);
            }
        }
    }

    spawnEnemy() {
        if (this.enemies.length >= this.maxEnemies) return;
        if (Math.random() > this.spawnRate) return;

        // 创建敌机
        const enemy = new THREE.Group();

        // 敌机机身
        const fuselageGeometry = new THREE.CylinderGeometry(0.2, 0.3, 3, 6);
        const fuselageMaterial = new THREE.MeshLambertMaterial({ color: 0xFF4444 });
        const fuselage = new THREE.Mesh(fuselageGeometry, fuselageMaterial);
        fuselage.rotation.z = Math.PI / 2;
        fuselage.castShadow = true;
        enemy.add(fuselage);

        // 敌机机翼
        const wingGeometry = new THREE.BoxGeometry(4, 0.1, 1);
        const wingMaterial = new THREE.MeshLambertMaterial({ color: 0xFF4444 });
        const wings = new THREE.Mesh(wingGeometry, wingMaterial);
        wings.castShadow = true;
        enemy.add(wings);

        // 随机生成位置
        const spawnDistance = 80;
        const angle = Math.random() * Math.PI * 2;
        enemy.position.set(
            Math.cos(angle) * spawnDistance,
            Math.random() * 20 - 10,
            Math.sin(angle) * spawnDistance
        );

        // 设置敌机属性
        enemy.userData.health = 1;
        enemy.userData.speed = this.enemySpeed;
        enemy.userData.type = 'enemy';

        this.enemies.push(enemy);
        this.scene.add(enemy);
    }

    updateEnemies() {
        for (let i = this.enemies.length - 1; i >= 0; i--) {
            const enemy = this.enemies[i];

            // 敌机AI - 朝向玩家移动
            const direction = new THREE.Vector3();
            direction.subVectors(this.player.position, enemy.position);
            direction.normalize();

            // 移动敌机
            enemy.position.add(direction.multiplyScalar(enemy.userData.speed));

            // 让敌机面向玩家
            enemy.lookAt(this.player.position);

            // 检查是否与玩家碰撞
            const distance = enemy.position.distanceTo(this.player.position);
            if (distance < 2) {
                // 玩家受到伤害
                this.takeDamage(20);

                // 创建爆炸效果
                this.createExplosion(enemy.position);

                // 播放爆炸音效
                if (this.audioManager) {
                    this.audioManager.playSound('explosion');
                }

                // 移除敌机
                this.scene.remove(enemy);
                this.enemies.splice(i, 1);
            }

            // 移除距离过远的敌机
            else if (distance > 150) {
                this.scene.remove(enemy);
                this.enemies.splice(i, 1);
            }
        }
    }

    checkCollisions() {
        // 检查炮弹与敌机的碰撞
        for (let i = this.bullets.length - 1; i >= 0; i--) {
            const bullet = this.bullets[i];

            for (let j = this.enemies.length - 1; j >= 0; j--) {
                const enemy = this.enemies[j];

                const distance = bullet.position.distanceTo(enemy.position);
                if (distance < 1.5) {
                    // 击中敌机
                    this.score += 100;

                    // 创建爆炸效果
                    this.createExplosion(enemy.position);

                    // 播放击中和爆炸音效
                    if (this.audioManager) {
                        this.audioManager.playSound('hit');
                        this.audioManager.playSound('explosion');
                    }

                    // 移除炮弹和敌机
                    this.scene.remove(bullet);
                    this.scene.remove(enemy);
                    this.bullets.splice(i, 1);
                    this.enemies.splice(j, 1);

                    break;
                }
            }
        }
    }

    createExplosion(position) {
        // 创建简单的爆炸效果
        const explosionGeometry = new THREE.SphereGeometry(2, 8, 8);
        const explosionMaterial = new THREE.MeshBasicMaterial({
            color: 0xFF6600,
            transparent: true,
            opacity: 0.8
        });
        const explosion = new THREE.Mesh(explosionGeometry, explosionMaterial);
        explosion.position.copy(position);

        // 设置爆炸动画属性
        explosion.userData.life = 1.0;
        explosion.userData.maxScale = 3;

        this.explosions.push(explosion);
        this.scene.add(explosion);

        // 创建粒子效果
        this.createParticles(position, 10);
    }

    updateExplosions() {
        for (let i = this.explosions.length - 1; i >= 0; i--) {
            const explosion = this.explosions[i];

            // 更新爆炸动画
            explosion.userData.life -= 0.05;

            if (explosion.userData.life <= 0) {
                // 移除爆炸效果
                this.scene.remove(explosion);
                this.explosions.splice(i, 1);
            } else {
                // 更新爆炸大小和透明度
                const scale = (1 - explosion.userData.life) * explosion.userData.maxScale;
                explosion.scale.set(scale, scale, scale);
                explosion.material.opacity = explosion.userData.life * 0.8;
            }
        }
    }

    createParticles(position, count) {
        for (let i = 0; i < count; i++) {
            const particleGeometry = new THREE.SphereGeometry(0.1, 4, 4);
            const particleMaterial = new THREE.MeshBasicMaterial({
                color: new THREE.Color().setHSL(Math.random() * 0.1 + 0.1, 1, 0.5),
                transparent: true,
                opacity: 1
            });
            const particle = new THREE.Mesh(particleGeometry, particleMaterial);

            particle.position.copy(position);
            particle.position.add(new THREE.Vector3(
                (Math.random() - 0.5) * 2,
                (Math.random() - 0.5) * 2,
                (Math.random() - 0.5) * 2
            ));

            // 设置粒子属性
            particle.userData.velocity = new THREE.Vector3(
                (Math.random() - 0.5) * 0.5,
                Math.random() * 0.3,
                (Math.random() - 0.5) * 0.5
            );
            particle.userData.life = 1.0;
            particle.userData.decay = 0.02;

            this.particles.push(particle);
            this.scene.add(particle);
        }
    }

    updateParticles() {
        for (let i = this.particles.length - 1; i >= 0; i--) {
            const particle = this.particles[i];

            // 更新粒子位置
            particle.position.add(particle.userData.velocity);

            // 应用重力
            particle.userData.velocity.y -= 0.01;

            // 更新生命值
            particle.userData.life -= particle.userData.decay;

            if (particle.userData.life <= 0) {
                // 移除粒子
                this.scene.remove(particle);
                this.particles.splice(i, 1);
            } else {
                // 更新透明度
                particle.material.opacity = particle.userData.life;
            }
        }
    }

    takeDamage(amount) {
        this.health -= amount;
        if (this.health <= 0) {
            this.health = 0;
            this.gameOver();
        }
    }

    gameOver() {
        this.gameState = 'gameOver';
        document.getElementById('finalScore').textContent = `最终得分: ${this.score}`;
        document.getElementById('gameOverScreen').style.display = 'block';
        document.getElementById('gameUI').style.display = 'none';
    }

    updateUI() {
        document.getElementById('score').textContent = `得分: ${this.score}`;
        document.getElementById('health').textContent = `生命值: ${this.health}`;
    }

    animate() {
        if (this.gameState === 'playing') {
            requestAnimationFrame(() => this.animate());

            const deltaTime = this.clock.getDelta();

            // 更新游戏逻辑
            this.updatePlayerMovement();
            this.updateBullets();
            this.updateEnemies();
            this.updateExplosions();
            this.updateParticles();
            this.checkCollisions();
            this.spawnEnemy();
            this.updateUI();

            // 渲染场景
            this.renderer.render(this.scene, this.camera);
        } else if (this.gameState === 'paused') {
            // 暂停状态下继续动画循环，但不更新游戏逻辑
            requestAnimationFrame(() => this.animate());
            this.renderer.render(this.scene, this.camera);
        }
    }
}

// 游戏初始化
let game;

// 等待页面加载完成
document.addEventListener('DOMContentLoaded', () => {
    // 检查Three.js是否加载
    if (typeof THREE === 'undefined') {
        console.error('Three.js 未能正确加载');
        return;
    }

    // 创建游戏实例
    game = new AirCombatGame();

    console.log('空战游戏已初始化');
});

// 防止页面刷新时的确认对话框
window.addEventListener('beforeunload', (event) => {
    if (game && game.gameState === 'playing') {
        event.preventDefault();
        event.returnValue = '';
    }
});
