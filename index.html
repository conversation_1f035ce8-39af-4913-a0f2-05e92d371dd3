<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>空战游戏 - Three.js</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div id="gameContainer">
        <!-- 游戏界面 -->
        <div id="gameUI">
            <div id="score">得分: 0</div>
            <div id="health">生命值: 100</div>
            <div id="ammo">弹药: ∞</div>
        </div>
        
        <!-- 游戏开始界面 -->
        <div id="startScreen">
            <h1>空战游戏</h1>
            <p>使用 WASD 控制飞机移动</p>
            <p>使用 空格键 发射炮弹</p>
            <p>使用 鼠标 控制视角</p>
            <button id="startButton">开始游戏</button>
        </div>
        
        <!-- 游戏结束界面 -->
        <div id="gameOverScreen" style="display: none;">
            <h1>游戏结束</h1>
            <p id="finalScore">最终得分: 0</p>
            <button id="restartButton">重新开始</button>
        </div>
        
        <!-- 暂停界面 -->
        <div id="pauseScreen" style="display: none;">
            <h1>游戏暂停</h1>
            <p>按 ESC 继续游戏</p>
        </div>
    </div>
    
    <!-- 加载Three.js -->
    <script src="three.min.js"></script>
    <script src="audio.js"></script>
    <script src="game.js"></script>
</body>
</html>
