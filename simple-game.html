<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化空战游戏</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            overflow: hidden;
            font-family: Arial, sans-serif;
        }
        
        #ui {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            z-index: 100;
            font-size: 18px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
        }
        
        #startScreen {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: white;
            z-index: 200;
            background: rgba(0, 0, 0, 0.8);
            padding: 40px;
            border-radius: 15px;
        }
        
        button {
            background: linear-gradient(45deg, #FF6B6B, #FF8E53);
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 20px;
            border-radius: 10px;
            cursor: pointer;
            margin-top: 20px;
        }
        
        button:hover {
            background: linear-gradient(45deg, #FF8E53, #FF6B6B);
        }
    </style>
</head>
<body>
    <div id="ui" style="display: none;">
        <div>得分: <span id="score">0</span></div>
        <div>生命值: <span id="health">100</span></div>
        <div>按 WASD 移动，空格射击，ESC 暂停</div>
    </div>
    
    <div id="startScreen">
        <h1>空战游戏</h1>
        <p>使用 WASD 控制飞机移动</p>
        <p>使用 空格键 发射炮弹</p>
        <p>使用 鼠标 控制视角</p>
        <button onclick="startGame()">开始游戏</button>
    </div>
    
    <script src="https://unpkg.com/three@0.180.0/build/three.min.js"></script>
    <script>
        let scene, camera, renderer, player;
        let bullets = [], enemies = [];
        let score = 0, health = 100;
        let keys = {};
        let gameRunning = false;
        
        function startGame() {
            console.log('开始游戏被调用');
            
            // 隐藏开始界面
            document.getElementById('startScreen').style.display = 'none';
            document.getElementById('ui').style.display = 'block';
            
            // 初始化Three.js
            initThreeJS();
            
            // 开始游戏循环
            gameRunning = true;
            animate();
            
            console.log('游戏已启动');
        }
        
        function initThreeJS() {
            console.log('初始化Three.js');
            
            // 创建场景
            scene = new THREE.Scene();
            scene.fog = new THREE.Fog(0x87CEEB, 50, 200);
            
            // 创建相机
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.set(0, 5, 10);
            
            // 创建渲染器
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.setClearColor(0x87CEEB);
            document.body.appendChild(renderer.domElement);
            
            // 添加光照
            const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
            scene.add(ambientLight);
            
            const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
            directionalLight.position.set(50, 50, 50);
            scene.add(directionalLight);
            
            // 创建玩家飞机
            createPlayer();
            
            // 设置事件监听
            setupControls();
            
            console.log('Three.js初始化完成');
        }
        
        function createPlayer() {
            player = new THREE.Group();
            
            // 机身
            const fuselageGeometry = new THREE.CylinderGeometry(0.3, 0.5, 4, 8);
            const fuselageMaterial = new THREE.MeshLambertMaterial({ color: 0x4169E1 });
            const fuselage = new THREE.Mesh(fuselageGeometry, fuselageMaterial);
            fuselage.rotation.z = Math.PI / 2;
            player.add(fuselage);
            
            // 机翼
            const wingGeometry = new THREE.BoxGeometry(6, 0.2, 1.5);
            const wingMaterial = new THREE.MeshLambertMaterial({ color: 0x4169E1 });
            const wings = new THREE.Mesh(wingGeometry, wingMaterial);
            player.add(wings);
            
            player.position.set(0, 0, 0);
            scene.add(player);
        }
        
        function setupControls() {
            document.addEventListener('keydown', (event) => {
                keys[event.code] = true;
                if (event.code === 'Space') {
                    event.preventDefault();
                    shootBullet();
                }
            });
            
            document.addEventListener('keyup', (event) => {
                keys[event.code] = false;
            });
        }
        
        function shootBullet() {
            const bulletGeometry = new THREE.SphereGeometry(0.1, 8, 8);
            const bulletMaterial = new THREE.MeshBasicMaterial({ color: 0xFFFF00 });
            const bullet = new THREE.Mesh(bulletGeometry, bulletMaterial);
            
            bullet.position.copy(player.position);
            bullet.userData.velocity = new THREE.Vector3(0, 0, -2);
            
            bullets.push(bullet);
            scene.add(bullet);
        }
        
        function updatePlayer() {
            const moveSpeed = 0.5;
            
            if (keys['KeyW']) player.position.z -= moveSpeed;
            if (keys['KeyS']) player.position.z += moveSpeed;
            if (keys['KeyA']) player.position.x -= moveSpeed;
            if (keys['KeyD']) player.position.x += moveSpeed;
            
            // 限制移动范围
            player.position.x = Math.max(-20, Math.min(20, player.position.x));
            player.position.z = Math.max(-20, Math.min(20, player.position.z));
            
            // 更新相机
            camera.position.set(player.position.x, player.position.y + 5, player.position.z + 10);
            camera.lookAt(player.position);
        }
        
        function updateBullets() {
            for (let i = bullets.length - 1; i >= 0; i--) {
                const bullet = bullets[i];
                bullet.position.add(bullet.userData.velocity);
                
                if (bullet.position.length() > 50) {
                    scene.remove(bullet);
                    bullets.splice(i, 1);
                }
            }
        }
        
        function spawnEnemy() {
            if (enemies.length < 5 && Math.random() < 0.01) {
                const enemy = new THREE.Group();
                
                const fuselageGeometry = new THREE.CylinderGeometry(0.2, 0.3, 3, 6);
                const fuselageMaterial = new THREE.MeshLambertMaterial({ color: 0xFF4444 });
                const fuselage = new THREE.Mesh(fuselageGeometry, fuselageMaterial);
                fuselage.rotation.z = Math.PI / 2;
                enemy.add(fuselage);
                
                const angle = Math.random() * Math.PI * 2;
                enemy.position.set(
                    Math.cos(angle) * 30,
                    Math.random() * 10 - 5,
                    Math.sin(angle) * 30
                );
                
                enemies.push(enemy);
                scene.add(enemy);
            }
        }
        
        function updateEnemies() {
            for (let i = enemies.length - 1; i >= 0; i--) {
                const enemy = enemies[i];
                
                // 朝向玩家移动
                const direction = new THREE.Vector3();
                direction.subVectors(player.position, enemy.position);
                direction.normalize();
                enemy.position.add(direction.multiplyScalar(0.2));
                
                // 检查碰撞
                const distance = enemy.position.distanceTo(player.position);
                if (distance < 2) {
                    health -= 20;
                    scene.remove(enemy);
                    enemies.splice(i, 1);
                    updateUI();
                    
                    if (health <= 0) {
                        alert('游戏结束！得分：' + score);
                        location.reload();
                    }
                }
            }
        }
        
        function checkCollisions() {
            for (let i = bullets.length - 1; i >= 0; i--) {
                const bullet = bullets[i];
                
                for (let j = enemies.length - 1; j >= 0; j--) {
                    const enemy = enemies[j];
                    
                    const distance = bullet.position.distanceTo(enemy.position);
                    if (distance < 1.5) {
                        score += 100;
                        scene.remove(bullet);
                        scene.remove(enemy);
                        bullets.splice(i, 1);
                        enemies.splice(j, 1);
                        updateUI();
                        break;
                    }
                }
            }
        }
        
        function updateUI() {
            document.getElementById('score').textContent = score;
            document.getElementById('health').textContent = health;
        }
        
        function animate() {
            if (!gameRunning) return;
            
            requestAnimationFrame(animate);
            
            updatePlayer();
            updateBullets();
            updateEnemies();
            spawnEnemy();
            checkCollisions();
            
            renderer.render(scene, camera);
        }
        
        // 窗口大小调整
        window.addEventListener('resize', () => {
            if (camera && renderer) {
                camera.aspect = window.innerWidth / window.innerHeight;
                camera.updateProjectionMatrix();
                renderer.setSize(window.innerWidth, window.innerHeight);
            }
        });
        
        console.log('脚本加载完成');
    </script>
</body>
</html>
